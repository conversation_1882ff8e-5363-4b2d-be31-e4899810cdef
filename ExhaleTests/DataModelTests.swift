//
//  DataModelTests.swift
//  ExhaleTests
//
//  Created by <PERSON> on 02.07.25.
//

import Testing
import Foundation

// Copy the data models directly here to avoid app dependency
struct SmokingEvent {
    let id = UUID()
    let timestamp: Date
    let confidence: Double
    let heartRateAtEvent: Double?
    
    init(timestamp: Date = Date(), confidence: Double, heartRateAtEvent: Double? = nil) {
        self.timestamp = timestamp
        self.confidence = confidence
        self.heartRateAtEvent = heartRateAtEvent
    }
}

struct HeartRateReading {
    let id = UUID()
    let timestamp: Date
    let heartRate: Double
    let source: String
    let isSmokingRelated: Bool
    
    init(timestamp: Date = Date(), heartRate: Double, source: String = "Apple Watch", isSmokingRelated: Bool = false) {
        self.timestamp = timestamp
        self.heartRate = heartRate
        self.source = source
        self.isSmokingRelated = isSmokingRelated
    }
}

struct DailySummary {
    let date: Date
    let smokingEventsCount: Int
    let averageHeartRate: Double
    let averageSmokingConfidence: Double
}

enum DetectionSensitivity: String, CaseIterable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
}

class UserSettings: ObservableObject {
    @Published var smokingDetectionEnabled: Bool = true
    @Published var heartRateMonitoringEnabled: Bool = true
    @Published var notificationsEnabled: Bool = true
    @Published var smokingGoal: Int = 0
    @Published var detectionSensitivity: DetectionSensitivity = .medium
    @Published var minimumConfidenceThreshold: Double = 0.7
}

class AppState: ObservableObject {
    @Published var smokingEvents: [SmokingEvent] = []
    @Published var heartRateReadings: [HeartRateReading] = []
    @Published var userSettings = UserSettings()
    
    func addSmokingEvent(_ event: SmokingEvent) {
        smokingEvents.append(event)
    }
    
    func addHeartRateReading(_ reading: HeartRateReading) {
        heartRateReadings.append(reading)
    }
    
    func generateDailySummary(for date: Date) -> DailySummary {
        let calendar = Calendar.current
        let dayEvents = smokingEvents.filter { calendar.isDate($0.timestamp, inSameDayAs: date) }
        let dayReadings = heartRateReadings.filter { calendar.isDate($0.timestamp, inSameDayAs: date) }
        
        let avgHeartRate = dayReadings.isEmpty ? 0.0 : dayReadings.map { $0.heartRate }.reduce(0, +) / Double(dayReadings.count)
        let avgConfidence = dayEvents.isEmpty ? 0.0 : dayEvents.map { $0.confidence }.reduce(0, +) / Double(dayEvents.count)
        
        return DailySummary(
            date: date,
            smokingEventsCount: dayEvents.count,
            averageHeartRate: avgHeartRate,
            averageSmokingConfidence: avgConfidence
        )
    }
    
    func loadData() {
        // Mock implementation for testing
    }
}

struct DataModelTests {

    @Test func testSmokingEventCreation() async throws {
        let event = SmokingEvent(confidence: 0.8, heartRateAtEvent: 75.0)

        #expect(event.confidence == 0.8)
        #expect(event.heartRateAtEvent == 75.0)
        #expect(event.id != nil)
        #expect(event.timestamp.timeIntervalSinceNow < 1.0) // Created recently
    }

    @Test func testHeartRateReadingCreation() async throws {
        let reading = HeartRateReading(heartRate: 72.0, isSmokingRelated: true)

        #expect(reading.heartRate == 72.0)
        #expect(reading.isSmokingRelated == true)
        #expect(reading.source == "Apple Watch")
        #expect(reading.id != nil)
    }

    @Test func testAppStateDataManagement() async throws {
        let appState = AppState()
        let event = SmokingEvent(confidence: 0.75)
        let reading = HeartRateReading(heartRate: 80.0)

        appState.addSmokingEvent(event)
        appState.addHeartRateReading(reading)

        #expect(appState.smokingEvents.count == 1)
        #expect(appState.heartRateReadings.count == 1)
        #expect(appState.smokingEvents.first?.confidence == 0.75)
        #expect(appState.heartRateReadings.first?.heartRate == 80.0)
    }

    @Test func testDailySummaryGeneration() async throws {
        let appState = AppState()
        let today = Date()
        let event1 = SmokingEvent(timestamp: today, confidence: 0.8)
        let event2 = SmokingEvent(timestamp: today, confidence: 0.9)
        let reading1 = HeartRateReading(timestamp: today, heartRate: 75.0)
        let reading2 = HeartRateReading(timestamp: today, heartRate: 85.0)

        appState.addSmokingEvent(event1)
        appState.addSmokingEvent(event2)
        appState.addHeartRateReading(reading1)
        appState.addHeartRateReading(reading2)

        let summary = appState.generateDailySummary(for: today)

        #expect(summary.smokingEventsCount == 2)
        #expect(summary.averageHeartRate == 80.0)
        #expect(summary.averageSmokingConfidence == 0.85)
    }

    @Test func testUserSettingsDefaults() async throws {
        let settings = UserSettings()

        #expect(settings.smokingDetectionEnabled == true)
        #expect(settings.heartRateMonitoringEnabled == true)
        #expect(settings.notificationsEnabled == true)
        #expect(settings.smokingGoal == 0)
        #expect(settings.detectionSensitivity == .medium)
        #expect(settings.minimumConfidenceThreshold == 0.7)
    }
}
