//
//  ExhaleTests.swift
//  ExhaleTests
//
//  Created by <PERSON> on 02.07.25.
//

import Testing
import HealthKit
@testable import Exhale

struct ExhaleTests {

    @Test func testSmokingEventCreation() async throws {
        let event = SmokingEvent(confidence: 0.8, heartRateAtEvent: 75.0)

        #expect(event.confidence == 0.8)
        #expect(event.heartRateAtEvent == 75.0)
        #expect(event.id != nil)
        #expect(event.timestamp.timeIntervalSinceNow < 1.0) // Created recently
    }

    @Test func testHeartRateReadingCreation() async throws {
        let reading = HeartRateReading(heartRate: 72.0, isSmokingRelated: true)

        #expect(reading.heartRate == 72.0)
        #expect(reading.isSmokingRelated == true)
        #expect(reading.source == "Apple Watch")
        #expect(reading.id != nil)
    }

    @Test func testAppStateDataManagement() async throws {
        let appState = AppState()
        let event = SmokingEvent(confidence: 0.75)
        let reading = HeartRateReading(heartRate: 80.0)

        appState.addSmokingEvent(event)
        appState.addHeartRateReading(reading)

        #expect(appState.smokingEvents.count == 1)
        #expect(appState.heartRateReadings.count == 1)
        #expect(appState.smokingEvents.first?.confidence == 0.75)
        #expect(appState.heartRateReadings.first?.heartRate == 80.0)
    }

    @Test func testDailySummaryGeneration() async throws {
        let appState = AppState()
        let today = Date()
        let event1 = SmokingEvent(timestamp: today, confidence: 0.8)
        let event2 = SmokingEvent(timestamp: today, confidence: 0.9)
        let reading1 = HeartRateReading(timestamp: today, heartRate: 75.0)
        let reading2 = HeartRateReading(timestamp: today, heartRate: 85.0)

        appState.addSmokingEvent(event1)
        appState.addSmokingEvent(event2)
        appState.addHeartRateReading(reading1)
        appState.addHeartRateReading(reading2)

        let summary = appState.generateDailySummary(for: today)

        #expect(summary.smokingEventsCount == 2)
        #expect(summary.averageHeartRate == 80.0)
        #expect(summary.averageSmokingConfidence == 0.85)
    }

    @Test func testUserSettingsDefaults() async throws {
        let settings = UserSettings()

        #expect(settings.smokingDetectionEnabled == true)
        #expect(settings.heartRateMonitoringEnabled == true)
        #expect(settings.notificationsEnabled == true)
        #expect(settings.smokingGoal == 0)
        #expect(settings.detectionSensitivity == .medium)
        #expect(settings.minimumConfidenceThreshold == 0.7)
    }

    // Commented out manager tests that might cause crashes in test environment
    // @Test func testMotionDetectionInitialization() async throws {
    //     let motionManager = MotionDetectionManager()
    //
    //     #expect(motionManager.isMonitoring == false)
    //     #expect(motionManager.smokingDetected == false)
    //     #expect(motionManager.smokingConfidence == 0.0)
    //     #expect(motionManager.lastSmokingEvent == nil)
    // }

}
