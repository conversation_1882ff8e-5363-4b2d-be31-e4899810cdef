//
//  ExhaleTests.swift
//  ExhaleTests
//
//  Created by <PERSON> on 02.07.25.
//

import Testing
import HealthKit
@testable import Exhale

struct ExhaleTests {

    // Test setup method to ensure we're using mock implementations
    func setupTestEnvironment() {
        let container = DependencyContainer.shared
        container.resetManagers()
        // The dependency container will automatically use mock implementations in test environment
    }

    @Test func testSmokingEventCreation() async throws {
        let event = SmokingEvent(confidence: 0.8, heartRateAtEvent: 75.0)

        #expect(event.confidence == 0.8)
        #expect(event.heartRateAtEvent == 75.0)
        #expect(event.id != nil)
        #expect(event.timestamp.timeIntervalSinceNow < 1.0) // Created recently
    }

    @Test func testHeartRateReadingCreation() async throws {
        let reading = HeartRateReading(heartRate: 72.0, isSmokingRelated: true)

        #expect(reading.heartRate == 72.0)
        #expect(reading.isSmokingRelated == true)
        #expect(reading.source == "Apple Watch")
        #expect(reading.id != nil)
    }

    @Test func testAppStateDataManagement() async throws {
        let appState = AppState()
        let event = SmokingEvent(confidence: 0.75)
        let reading = HeartRateReading(heartRate: 80.0)

        appState.addSmokingEvent(event)
        appState.addHeartRateReading(reading)

        #expect(appState.smokingEvents.count == 1)
        #expect(appState.heartRateReadings.count == 1)
        #expect(appState.smokingEvents.first?.confidence == 0.75)
        #expect(appState.heartRateReadings.first?.heartRate == 80.0)
    }

    @Test func testDailySummaryGeneration() async throws {
        let appState = AppState()
        let today = Date()
        let event1 = SmokingEvent(timestamp: today, confidence: 0.8)
        let event2 = SmokingEvent(timestamp: today, confidence: 0.9)
        let reading1 = HeartRateReading(timestamp: today, heartRate: 75.0)
        let reading2 = HeartRateReading(timestamp: today, heartRate: 85.0)

        appState.addSmokingEvent(event1)
        appState.addSmokingEvent(event2)
        appState.addHeartRateReading(reading1)
        appState.addHeartRateReading(reading2)

        let summary = appState.generateDailySummary(for: today)

        #expect(summary.smokingEventsCount == 2)
        #expect(summary.averageHeartRate == 80.0)
        #expect(summary.averageSmokingConfidence == 0.85)
    }

    @Test func testUserSettingsDefaults() async throws {
        let settings = UserSettings()

        #expect(settings.smokingDetectionEnabled == true)
        #expect(settings.heartRateMonitoringEnabled == true)
        #expect(settings.notificationsEnabled == true)
        #expect(settings.smokingGoal == 0)
        #expect(settings.detectionSensitivity == .medium)
        #expect(settings.minimumConfidenceThreshold == 0.7)
    }

    // MARK: - Manager Tests with Dependency Injection

    @Test func testHealthKitManagerMock() async throws {
        setupTestEnvironment()

        let container = DependencyContainer.shared
        let healthManager = container.healthKitManager()

        // Test that we get a mock implementation in test environment
        #expect(healthManager is MockHealthKitManager)

        // Test mock behavior
        #expect(healthManager.isAuthorized == true)
        #expect(healthManager.heartRate > 0) // Mock should provide simulated heart rate
    }

    @Test func testMotionDetectionManagerMock() async throws {
        setupTestEnvironment()

        let container = DependencyContainer.shared
        let motionManager = container.motionDetectionManager()

        // Test that we get a mock implementation in test environment
        #expect(motionManager is MockMotionDetectionManager)

        // Test initial state
        #expect(motionManager.isMonitoring == false)
        #expect(motionManager.smokingDetected == false)
        #expect(motionManager.smokingConfidence == 0.0)
    }

    @Test func testNotificationManagerMock() async throws {
        setupTestEnvironment()

        let container = DependencyContainer.shared
        let notificationManager = container.notificationManager()

        // Test that we get a mock implementation in test environment
        #expect(notificationManager is MockNotificationManager)

        // Test notification sending
        if let mockManager = notificationManager as? MockNotificationManager {
            mockManager.sendSmokingDetectedNotification(confidence: 0.8, heartRate: 75.0)

            #expect(mockManager.getNotificationCount(ofType: "smoking_detection") == 1)

            let lastNotification = mockManager.getLastNotification(ofType: "smoking_detection")
            #expect(lastNotification != nil)
            #expect(lastNotification?.title == "Smoking Event Detected")
        }
    }

}
