//
//  DependencyContainer.swift
//  Exhale
//
//  Created by <PERSON> on 02.07.25.
//

import Foundation
import SwiftUI
import Combine
import HealthKit

// MARK: - Environment Detection

class EnvironmentDetector {
    static let shared = EnvironmentDetector()
    
    private init() {}
    
    var isTestEnvironment: Bool {
        // Multiple detection methods for robustness
        return isRunningTests() || isRunningInTestHost() || isXCTestEnvironment()
    }
    
    private func isRunningTests() -> Bool {
        return NSClassFromString("XCTest") != nil
    }
    
    private func isRunningInTestHost() -> Bool {
        return ProcessInfo.processInfo.environment["XCTestConfigurationFilePath"] != nil
    }
    
    private func isXCTestEnvironment() -> Bool {
        return ProcessInfo.processInfo.arguments.contains { $0.contains("xctest") }
    }
    
    var isPreviewEnvironment: Bool {
        return ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1"
    }
    
    var shouldUseMockServices: Bool {
        return isTestEnvironment || isPreviewEnvironment
    }
}

// MARK: - Dependency Container

class DependencyContainer: ObservableObject {
    static let shared = DependencyContainer()
    
    private let environmentDetector = EnvironmentDetector.shared
    
    // Lazy initialization to avoid system service access during container creation
    private var _healthKitManager: (any HealthKitManagerProtocol)?
    private var _motionDetectionManager: (any MotionDetectionManagerProtocol)?
    private var _notificationManager: (any NotificationManagerProtocol)?
    
    private init() {}
    
    // MARK: - Manager Factories
    
    func healthKitManager() -> any HealthKitManagerProtocol {
        if let existing = _healthKitManager {
            return existing
        }
        
        let manager: any HealthKitManagerProtocol
        
        if environmentDetector.shouldUseMockServices {
            manager = MockHealthKitManager()
        } else {
            manager = HealthKitManager()
        }
        
        _healthKitManager = manager
        return manager
    }
    
    func motionDetectionManager() -> any MotionDetectionManagerProtocol {
        if let existing = _motionDetectionManager {
            return existing
        }
        
        let manager: any MotionDetectionManagerProtocol
        
        if environmentDetector.shouldUseMockServices {
            manager = MockMotionDetectionManager()
        } else {
            manager = MotionDetectionManager()
        }
        
        _motionDetectionManager = manager
        return manager
    }
    
    func notificationManager() -> any NotificationManagerProtocol {
        if environmentDetector.shouldUseMockServices {
            if let existing = _notificationManager {
                return existing
            }
            let manager = MockNotificationManager()
            _notificationManager = manager
            return manager
        } else {
            // For production, use the singleton pattern
            return NotificationManager.shared
        }
    }
    
    // MARK: - Reset Methods (for testing)
    
    func resetManagers() {
        _healthKitManager = nil
        _motionDetectionManager = nil
        _notificationManager = nil
    }
    
    // MARK: - Manual Override (for testing specific scenarios)
    
    func setHealthKitManager(_ manager: any HealthKitManagerProtocol) {
        _healthKitManager = manager
    }
    
    func setMotionDetectionManager(_ manager: any MotionDetectionManagerProtocol) {
        _motionDetectionManager = manager
    }
    
    func setNotificationManager(_ manager: any NotificationManagerProtocol) {
        _notificationManager = manager
    }
}

// MARK: - SwiftUI Environment Key

struct DependencyContainerKey: EnvironmentKey {
    static let defaultValue = DependencyContainer.shared
}

extension EnvironmentValues {
    var dependencyContainer: DependencyContainer {
        get { self[DependencyContainerKey.self] }
        set { self[DependencyContainerKey.self] = newValue }
    }
}

// MARK: - View Extension for Easy Access

extension View {
    func withDependencyContainer(_ container: DependencyContainer = DependencyContainer.shared) -> some View {
        self.environment(\.dependencyContainer, container)
    }
}

// MARK: - Manager Wrapper Classes for SwiftUI StateObject

// These wrapper classes are needed because SwiftUI's @StateObject requires concrete types
// that conform to ObservableObject, but our protocols use 'any' which doesn't work with @StateObject

class HealthKitManagerWrapper: ObservableObject {
    private let _manager: any HealthKitManagerProtocol
    private var cancellables = Set<AnyCancellable>()

    @Published var heartRate: Double = 0.0
    @Published var isAuthorized: Bool = false
    @Published var authorizationError: String? = nil

    var manager: any HealthKitManagerProtocol & ObservableObject {
        return _manager as! any HealthKitManagerProtocol & ObservableObject
    }
    
    init(manager: any HealthKitManagerProtocol) {
        self._manager = manager
        setupBindings()
    }
    
    private func setupBindings() {
        // Bind the wrapper's published properties to the manager's properties
        if let observableManager = _manager as? any ObservableObject {
            // Use reflection to bind properties - this is a workaround for protocol limitations
            // In a real implementation, you might want to use a more robust binding mechanism
            Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
                self.heartRate = self._manager.heartRate
                self.isAuthorized = self._manager.isAuthorized
                self.authorizationError = self._manager.authorizationError
            }
        }
    }
    
    func requestAuthorization() {
        _manager.requestAuthorization()
    }

    func startHeartRateMonitoring() {
        _manager.startHeartRateMonitoring()
    }

    func stopHeartRateMonitoring() {
        _manager.stopHeartRateMonitoring()
    }

    func fetchHeartRateHistory(from startDate: Date, to endDate: Date, completion: @escaping ([HKQuantitySample]) -> Void) {
        _manager.fetchHeartRateHistory(from: startDate, to: endDate, completion: completion)
    }
}

class MotionDetectionManagerWrapper: ObservableObject {
    private let _manager: any MotionDetectionManagerProtocol

    @Published var isMonitoring: Bool = false
    @Published var smokingDetected: Bool = false
    @Published var lastSmokingEvent: Date? = nil
    @Published var smokingConfidence: Double = 0.0

    var manager: any MotionDetectionManagerProtocol & ObservableObject {
        return _manager as! any MotionDetectionManagerProtocol & ObservableObject
    }
    
    init(manager: any MotionDetectionManagerProtocol) {
        self._manager = manager
        setupBindings()
    }
    
    private func setupBindings() {
        Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
            self.isMonitoring = self._manager.isMonitoring
            self.smokingDetected = self._manager.smokingDetected
            self.lastSmokingEvent = self._manager.lastSmokingEvent
            self.smokingConfidence = self._manager.smokingConfidence
        }
    }

    func startMotionMonitoring() {
        _manager.startMotionMonitoring()
    }

    func stopMotionMonitoring() {
        _manager.stopMotionMonitoring()
    }
}

class NotificationManagerWrapper: ObservableObject {
    private let _manager: any NotificationManagerProtocol

    var manager: any NotificationManagerProtocol {
        return _manager
    }

    init(manager: any NotificationManagerProtocol) {
        self._manager = manager
    }
    
    func sendSmokingDetectedNotification(confidence: Double, heartRate: Double?) {
        _manager.sendSmokingDetectedNotification(confidence: confidence, heartRate: heartRate)
    }

    func sendHeartRateAlertNotification(heartRate: Double, threshold: Double) {
        _manager.sendHeartRateAlertNotification(heartRate: heartRate, threshold: threshold)
    }

    func sendDailySummaryNotification(smokingEvents: Int, averageHeartRate: Double?) {
        _manager.sendDailySummaryNotification(smokingEvents: smokingEvents, averageHeartRate: averageHeartRate)
    }

    func sendGoalAchievementNotification(goalType: String, achievement: String) {
        _manager.sendGoalAchievementNotification(goalType: goalType, achievement: achievement)
    }

    func scheduleReminderNotification(title: String, body: String, timeInterval: TimeInterval) {
        _manager.scheduleReminderNotification(title: title, body: body, timeInterval: timeInterval)
    }

    func requestNotificationPermission(completion: @escaping (Bool) -> Void) {
        _manager.requestNotificationPermission(completion: completion)
    }

    func clearAllNotifications() {
        _manager.clearAllNotifications()
    }

    func checkNotificationPermissions(completion: @escaping (Bool) -> Void) {
        _manager.checkNotificationPermissions(completion: completion)
    }
}
