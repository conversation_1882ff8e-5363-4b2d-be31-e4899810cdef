//
//  ExhaleApp.swift
//  Exhale
//
//  Created by <PERSON> on 02.07.25.
//

import SwiftUI
import HealthKit
import UserNotifications

@main
struct ExhaleApp: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
                .onAppear {
                    requestNotificationPermissions()
                }
        }
    }

    private func requestNotificationPermissions() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            if let error = error {
                print("Notification permission error: \(error.localizedDescription)")
            }
        }
    }
}
