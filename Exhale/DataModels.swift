//
//  DataModels.swift
//  Exhale
//
//  Created by <PERSON> on 02.07.25.
//

import Foundation
import HealthKit

// MARK: - Smoking Event Model

struct SmokingEvent: Identifiable, Codable {
    let id = UUID()
    let timestamp: Date
    let confidence: Double
    let heartRateAtEvent: Double?
    let duration: TimeInterval? // Duration of the smoking session
    let location: LocationData?
    
    init(timestamp: Date = Date(), confidence: Double, heartRateAtEvent: Double? = nil, duration: TimeInterval? = nil, location: LocationData? = nil) {
        self.timestamp = timestamp
        self.confidence = confidence
        self.heartRateAtEvent = heartRateAtEvent
        self.duration = duration
        self.location = location
    }
}

// MARK: - Heart Rate Data Model

struct HeartRateReading: Identifiable, Codable {
    let id = UUID()
    let timestamp: Date
    let heartRate: Double
    let source: String // e.g., "Apple Watch", "Manual Entry"
    let isSmokingRelated: Bool // Whether this reading was taken during/after smoking
    
    init(timestamp: Date = Date(), heartRate: Double, source: String = "Apple Watch", isSmokingRelated: Bool = false) {
        self.timestamp = timestamp
        self.heartRate = heartRate
        self.source = source
        self.isSmokingRelated = isSmokingRelated
    }
    
    // Convert from HealthKit sample
    init(from sample: HKQuantitySample, isSmokingRelated: Bool = false) {
        self.timestamp = sample.endDate
        let heartRateUnit = HKUnit.count().unitDivided(by: HKUnit.minute())
        self.heartRate = sample.quantity.doubleValue(for: heartRateUnit)
        self.source = sample.sourceRevision.source.name
        self.isSmokingRelated = isSmokingRelated
    }
}

// MARK: - Location Data Model

struct LocationData: Codable {
    let latitude: Double
    let longitude: Double
    let address: String?
    
    init(latitude: Double, longitude: Double, address: String? = nil) {
        self.latitude = latitude
        self.longitude = longitude
        self.address = address
    }
}

// MARK: - Daily Summary Model

struct DailySummary: Identifiable, Codable {
    let id = UUID()
    let date: Date
    let smokingEvents: [SmokingEvent]
    let averageHeartRate: Double
    let maxHeartRate: Double
    let minHeartRate: Double
    let smokingFrequency: Int
    let totalSmokingDuration: TimeInterval
    
    var smokingEventsCount: Int {
        smokingEvents.count
    }
    
    var averageSmokingConfidence: Double {
        guard !smokingEvents.isEmpty else { return 0.0 }
        return smokingEvents.map { $0.confidence }.reduce(0, +) / Double(smokingEvents.count)
    }
    
    init(date: Date, smokingEvents: [SmokingEvent], heartRateReadings: [HeartRateReading]) {
        self.date = date
        self.smokingEvents = smokingEvents
        self.smokingFrequency = smokingEvents.count
        self.totalSmokingDuration = smokingEvents.compactMap { $0.duration }.reduce(0, +)
        
        if !heartRateReadings.isEmpty {
            self.averageHeartRate = heartRateReadings.map { $0.heartRate }.reduce(0, +) / Double(heartRateReadings.count)
            self.maxHeartRate = heartRateReadings.map { $0.heartRate }.max() ?? 0
            self.minHeartRate = heartRateReadings.map { $0.heartRate }.min() ?? 0
        } else {
            self.averageHeartRate = 0
            self.maxHeartRate = 0
            self.minHeartRate = 0
        }
    }
}

// MARK: - Weekly Summary Model

struct WeeklySummary: Identifiable, Codable {
    let id = UUID()
    let weekStartDate: Date
    let weekEndDate: Date
    let dailySummaries: [DailySummary]
    
    var totalSmokingEvents: Int {
        dailySummaries.map { $0.smokingEventsCount }.reduce(0, +)
    }
    
    var averageDailySmokingEvents: Double {
        guard !dailySummaries.isEmpty else { return 0.0 }
        return Double(totalSmokingEvents) / Double(dailySummaries.count)
    }
    
    var averageWeeklyHeartRate: Double {
        let allHeartRates = dailySummaries.compactMap { summary in
            summary.averageHeartRate > 0 ? summary.averageHeartRate : nil
        }
        guard !allHeartRates.isEmpty else { return 0.0 }
        return allHeartRates.reduce(0, +) / Double(allHeartRates.count)
    }
    
    var totalSmokingDuration: TimeInterval {
        dailySummaries.map { $0.totalSmokingDuration }.reduce(0, +)
    }
    
    init(weekStartDate: Date, dailySummaries: [DailySummary]) {
        self.weekStartDate = weekStartDate
        self.weekEndDate = Calendar.current.date(byAdding: .day, value: 6, to: weekStartDate) ?? weekStartDate
        self.dailySummaries = dailySummaries
    }
}

// MARK: - User Settings Model

struct UserSettings: Codable {
    var smokingDetectionEnabled: Bool = true
    var heartRateMonitoringEnabled: Bool = true
    var notificationsEnabled: Bool = true
    var smokingGoal: Int = 0 // Target number of cigarettes per day (0 = no goal)
    var reminderInterval: TimeInterval = 3600 // Reminder interval in seconds
    var privacyMode: Bool = false // Hide sensitive data in notifications
    
    // Detection sensitivity settings
    var detectionSensitivity: DetectionSensitivity = .medium
    var minimumConfidenceThreshold: Double = 0.7
    
    enum DetectionSensitivity: String, CaseIterable, Codable {
        case low = "Low"
        case medium = "Medium"
        case high = "High"
        
        var threshold: Double {
            switch self {
            case .low: return 0.8
            case .medium: return 0.7
            case .high: return 0.6
            }
        }
    }
}

// MARK: - App State Model

class AppState: ObservableObject {
    @Published var smokingEvents: [SmokingEvent] = []
    @Published var heartRateReadings: [HeartRateReading] = []
    @Published var userSettings = UserSettings()
    @Published var isMonitoring = false
    
    // Computed properties
    var todaysSmokingEvents: [SmokingEvent] {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        
        return smokingEvents.filter { event in
            event.timestamp >= today && event.timestamp < tomorrow
        }
    }
    
    var todaysHeartRateReadings: [HeartRateReading] {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        
        return heartRateReadings.filter { reading in
            reading.timestamp >= today && reading.timestamp < tomorrow
        }
    }
    
    func addSmokingEvent(_ event: SmokingEvent) {
        smokingEvents.append(event)
        saveData()
    }
    
    func addHeartRateReading(_ reading: HeartRateReading) {
        heartRateReadings.append(reading)
        saveData()
    }
    
    func generateDailySummary(for date: Date) -> DailySummary {
        let startOfDay = Calendar.current.startOfDay(for: date)
        let endOfDay = Calendar.current.date(byAdding: .day, value: 1, to: startOfDay)!
        
        let daySmokingEvents = smokingEvents.filter { event in
            event.timestamp >= startOfDay && event.timestamp < endOfDay
        }
        
        let dayHeartRateReadings = heartRateReadings.filter { reading in
            reading.timestamp >= startOfDay && reading.timestamp < endOfDay
        }
        
        return DailySummary(date: date, smokingEvents: daySmokingEvents, heartRateReadings: dayHeartRateReadings)
    }
    
    // MARK: - Data Persistence
    
    private func saveData() {
        // Save to UserDefaults or Core Data
        if let encoded = try? JSONEncoder().encode(smokingEvents) {
            UserDefaults.standard.set(encoded, forKey: "smokingEvents")
        }
        
        if let encoded = try? JSONEncoder().encode(heartRateReadings) {
            UserDefaults.standard.set(encoded, forKey: "heartRateReadings")
        }
        
        if let encoded = try? JSONEncoder().encode(userSettings) {
            UserDefaults.standard.set(encoded, forKey: "userSettings")
        }
    }
    
    func loadData() {
        if let data = UserDefaults.standard.data(forKey: "smokingEvents"),
           let decoded = try? JSONDecoder().decode([SmokingEvent].self, from: data) {
            smokingEvents = decoded
        }
        
        if let data = UserDefaults.standard.data(forKey: "heartRateReadings"),
           let decoded = try? JSONDecoder().decode([HeartRateReading].self, from: data) {
            heartRateReadings = decoded
        }
        
        if let data = UserDefaults.standard.data(forKey: "userSettings"),
           let decoded = try? JSONDecoder().decode(UserSettings.self, from: data) {
            userSettings = decoded
        }
    }
}
