//
//  DashboardView.swift
//  Exhale
//
//  Created by <PERSON> on 02.07.25.
//

import SwiftUI

struct DashboardView: View {
    @ObservedObject var healthKitManager: HealthKitManager
    @ObservedObject var motionManager: MotionDetectionManager
    @ObservedObject var appState: AppState
    
    @State private var showingPermissionAlert = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    headerSection
                    
                    // Monitoring Status
                    monitoringStatusSection
                    
                    // Current Stats
                    currentStatsSection
                    
                    // Today's Summary
                    todaysSummarySection
                    
                    // Quick Actions
                    quickActionsSection
                }
                .padding()
            }
            .navigationTitle("Exhale")
            .onAppear {
                requestPermissionsIfNeeded()
            }
            .alert("Permissions Required", isPresented: $showingPermissionAlert) {
                Button("Settings") {
                    if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                        UIApplication.shared.open(settingsUrl)
                    }
                }
                But<PERSON>("Cancel", role: .cancel) { }
            } message: {
                Text("This app requires access to HealthKit and Motion sensors to detect smoking events and monitor heart rate. Please enable these permissions in Settings.")
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 8) {
            Image(systemName: "lungs.fill")
                .font(.system(size: 50))
                .foregroundColor(.blue)
            
            Text("Smoking Detection & Heart Rate Monitor")
                .font(.headline)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
        }
        .padding()
    }
    
    private var monitoringStatusSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Monitoring Status")
                    .font(.headline)
                Spacer()
            }
            
            HStack(spacing: 20) {
                StatusCard(
                    title: "Heart Rate",
                    status: healthKitManager.isAuthorized ? "Active" : "Inactive",
                    isActive: healthKitManager.isAuthorized,
                    icon: "heart.fill"
                )
                
                StatusCard(
                    title: "Motion Detection",
                    status: motionManager.isMonitoring ? "Active" : "Inactive",
                    isActive: motionManager.isMonitoring,
                    icon: "figure.walk"
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var currentStatsSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Current Readings")
                    .font(.headline)
                Spacer()
            }
            
            HStack(spacing: 20) {
                StatCard(
                    title: "Heart Rate",
                    value: healthKitManager.heartRate > 0 ? "\(Int(healthKitManager.heartRate))" : "--",
                    unit: "BPM",
                    icon: "heart.fill",
                    color: .red
                )
                
                StatCard(
                    title: "Detection Confidence",
                    value: motionManager.smokingConfidence > 0 ? "\(Int(motionManager.smokingConfidence * 100))" : "--",
                    unit: "%",
                    icon: "waveform.path.ecg",
                    color: .orange
                )
            }
            
            if motionManager.smokingDetected {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.red)
                    Text("Smoking Event Detected!")
                        .font(.headline)
                        .foregroundColor(.red)
                    Spacer()
                }
                .padding()
                .background(Color.red.opacity(0.1))
                .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var todaysSummarySection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Today's Summary")
                    .font(.headline)
                Spacer()
                Text(Date(), style: .date)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            let todaysEvents = appState.todaysSmokingEvents
            let todaysHeartRate = appState.todaysHeartRateReadings
            
            HStack(spacing: 15) {
                SummaryCard(
                    title: "Smoking Events",
                    value: "\(todaysEvents.count)",
                    icon: "smoke.fill",
                    color: .orange
                )
                
                SummaryCard(
                    title: "Avg Heart Rate",
                    value: todaysHeartRate.isEmpty ? "--" : "\(Int(todaysHeartRate.map { $0.heartRate }.reduce(0, +) / Double(todaysHeartRate.count)))",
                    icon: "heart.fill",
                    color: .red
                )
                
                SummaryCard(
                    title: "Last Event",
                    value: todaysEvents.last?.timestamp.formatted(date: .omitted, time: .shortened) ?? "--",
                    icon: "clock.fill",
                    color: .blue
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var quickActionsSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Quick Actions")
                    .font(.headline)
                Spacer()
            }
            
            HStack(spacing: 15) {
                ActionButton(
                    title: motionManager.isMonitoring ? "Stop Monitoring" : "Start Monitoring",
                    icon: motionManager.isMonitoring ? "stop.fill" : "play.fill",
                    color: motionManager.isMonitoring ? .red : .green
                ) {
                    toggleMonitoring()
                }
                
                ActionButton(
                    title: "View History",
                    icon: "chart.line.uptrend.xyaxis",
                    color: .blue
                ) {
                    // Switch to history tab
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private func requestPermissionsIfNeeded() {
        if !healthKitManager.isAuthorized {
            healthKitManager.requestAuthorization()
        }
    }
    
    private func toggleMonitoring() {
        if motionManager.isMonitoring {
            motionManager.stopMotionMonitoring()
            healthKitManager.stopHeartRateMonitoring()
            appState.isMonitoring = false
        } else {
            if healthKitManager.isAuthorized {
                motionManager.startMotionMonitoring()
                healthKitManager.startHeartRateMonitoring()
                appState.isMonitoring = true
            } else {
                showingPermissionAlert = true
            }
        }
    }
}

// MARK: - Supporting Views

struct StatusCard: View {
    let title: String
    let status: String
    let isActive: Bool
    let icon: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(isActive ? .green : .gray)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(status)
                .font(.caption2)
                .fontWeight(.semibold)
                .foregroundColor(isActive ? .green : .red)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let unit: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            HStack(alignment: .bottom, spacing: 2) {
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text(unit)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
}

struct SummaryCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .padding(.horizontal, 8)
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
}

struct ActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                Text(title)
                    .fontWeight(.semibold)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(color)
            .foregroundColor(.white)
            .cornerRadius(8)
        }
    }
}

#Preview {
    DashboardView(
        healthKitManager: HealthKitManager(),
        motionManager: MotionDetectionManager(),
        appState: AppState()
    )
}
