//
//  HistoryView.swift
//  Exhale
//
//  Created by <PERSON> on 02.07.25.
//

import SwiftUI

struct HistoryView: View {
    @ObservedObject var appState: AppState
    @State private var selectedTimeRange: TimeRange = .week
    @State private var selectedDate = Date()
    
    enum TimeRange: String, CaseIterable {
        case day = "Day"
        case week = "Week"
        case month = "Month"
        
        var days: Int {
            switch self {
            case .day: return 1
            case .week: return 7
            case .month: return 30
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Time Range Selector
                    timeRangeSelector
                    
                    // Charts Section
                    chartsSection
                    
                    // Statistics Section
                    statisticsSection
                    
                    // Events List
                    eventsListSection
                }
                .padding()
            }
            .navigationTitle("History")
        }
    }
    
    private var timeRangeSelector: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Time Range")
                    .font(.headline)
                Spacer()
            }
            
            Picker("Time Range", selection: $selectedTimeRange) {
                ForEach(TimeRange.allCases, id: \.self) { range in
                    Text(range.rawValue).tag(range)
                }
            }
            .picker<PERSON>tyle(SegmentedPickerStyle())
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var chartsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Trends")
                    .font(.headline)
                Spacer()
            }
            
            // Smoking Events Chart
            smokingEventsChart
            
            // Heart Rate Chart
            heartRateChart
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var smokingEventsChart: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Daily Smoking Events")
                .font(.subheadline)
                .fontWeight(.semibold)

            let chartData = getSmokingEventsChartData()

            if chartData.isEmpty {
                Text("No data available")
                    .foregroundColor(.secondary)
                    .frame(height: 150)
                    .frame(maxWidth: .infinity)
            } else {
                HStack(alignment: .bottom, spacing: 4) {
                    ForEach(chartData, id: \.date) { dataPoint in
                        VStack(spacing: 4) {
                            Rectangle()
                                .fill(.orange)
                                .frame(width: 20, height: max(CGFloat(dataPoint.count) * 15, 2))

                            Text("\(dataPoint.count)")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .frame(height: 150)
                .frame(maxWidth: .infinity)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
    
    private var heartRateChart: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Average Heart Rate Trend")
                .font(.subheadline)
                .fontWeight(.semibold)

            let chartData = getHeartRateChartData()

            if chartData.isEmpty {
                Text("No data available")
                    .foregroundColor(.secondary)
                    .frame(height: 150)
                    .frame(maxWidth: .infinity)
            } else {
                HStack(alignment: .bottom, spacing: 8) {
                    ForEach(Array(chartData.enumerated()), id: \.offset) { index, dataPoint in
                        VStack(spacing: 4) {
                            Circle()
                                .fill(.red)
                                .frame(width: 8, height: 8)
                                .offset(y: -CGFloat(dataPoint.heartRate - 60) * 0.5)

                            Text("\(Int(dataPoint.heartRate))")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .frame(height: 150)
                .frame(maxWidth: .infinity)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
    
    private var statisticsSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Statistics")
                    .font(.headline)
                Spacer()
            }
            
            let stats = calculateStatistics()
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 15) {
                StatisticCard(
                    title: "Total Events",
                    value: "\(stats.totalEvents)",
                    icon: "smoke.fill",
                    color: .orange
                )
                
                StatisticCard(
                    title: "Avg per Day",
                    value: String(format: "%.1f", stats.averagePerDay),
                    icon: "calendar",
                    color: .blue
                )
                
                StatisticCard(
                    title: "Avg Heart Rate",
                    value: stats.averageHeartRate > 0 ? "\(Int(stats.averageHeartRate))" : "--",
                    icon: "heart.fill",
                    color: .red
                )
                
                StatisticCard(
                    title: "Peak Day",
                    value: "\(stats.peakDayEvents)",
                    icon: "arrow.up.circle.fill",
                    color: .green
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var eventsListSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Recent Events")
                    .font(.headline)
                Spacer()
            }
            
            let recentEvents = getRecentEvents()
            
            if recentEvents.isEmpty {
                Text("No events recorded")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, minHeight: 100)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(recentEvents) { event in
                        EventRow(event: event)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - Data Processing
    
    private func getSmokingEventsChartData() -> [ChartDataPoint] {
        let endDate = selectedDate
        let startDate = Calendar.current.date(byAdding: .day, value: -selectedTimeRange.days, to: endDate) ?? endDate
        
        let filteredEvents = appState.smokingEvents.filter { event in
            event.timestamp >= startDate && event.timestamp <= endDate
        }
        
        var chartData: [ChartDataPoint] = []
        let calendar = Calendar.current
        
        for i in 0..<min(selectedTimeRange.days, 7) { // Limit to 7 days for display
            let date = calendar.date(byAdding: .day, value: -i, to: endDate) ?? endDate
            let dayStart = calendar.startOfDay(for: date)
            let dayEnd = calendar.date(byAdding: .day, value: 1, to: dayStart) ?? dayStart
            
            let dayEvents = filteredEvents.filter { event in
                event.timestamp >= dayStart && event.timestamp < dayEnd
            }
            
            chartData.append(ChartDataPoint(date: date, count: dayEvents.count, heartRate: 0))
        }
        
        return chartData.reversed()
    }
    
    private func getHeartRateChartData() -> [HeartRateChartDataPoint] {
        let endDate = selectedDate
        let startDate = Calendar.current.date(byAdding: .day, value: -selectedTimeRange.days, to: endDate) ?? endDate
        
        let filteredReadings = appState.heartRateReadings.filter { reading in
            reading.timestamp >= startDate && reading.timestamp <= endDate
        }
        
        var chartData: [HeartRateChartDataPoint] = []
        let calendar = Calendar.current
        
        for i in 0..<min(selectedTimeRange.days, 7) { // Limit to 7 days for display
            let date = calendar.date(byAdding: .day, value: -i, to: endDate) ?? endDate
            let dayStart = calendar.startOfDay(for: date)
            let dayEnd = calendar.date(byAdding: .day, value: 1, to: dayStart) ?? dayStart
            
            let dayReadings = filteredReadings.filter { reading in
                reading.timestamp >= dayStart && reading.timestamp < dayEnd
            }
            
            if !dayReadings.isEmpty {
                let averageHeartRate = dayReadings.map { $0.heartRate }.reduce(0, +) / Double(dayReadings.count)
                chartData.append(HeartRateChartDataPoint(date: date, heartRate: averageHeartRate))
            }
        }
        
        return chartData.reversed()
    }
    
    private func calculateStatistics() -> Statistics {
        let endDate = selectedDate
        let startDate = Calendar.current.date(byAdding: .day, value: -selectedTimeRange.days, to: endDate) ?? endDate
        
        let filteredEvents = appState.smokingEvents.filter { event in
            event.timestamp >= startDate && event.timestamp <= endDate
        }
        
        let filteredReadings = appState.heartRateReadings.filter { reading in
            reading.timestamp >= startDate && reading.timestamp <= endDate
        }
        
        let totalEvents = filteredEvents.count
        let averagePerDay = Double(totalEvents) / Double(selectedTimeRange.days)
        let averageHeartRate = filteredReadings.isEmpty ? 0 : filteredReadings.map { $0.heartRate }.reduce(0, +) / Double(filteredReadings.count)
        
        // Calculate peak day
        var dailyEventCounts: [Int] = []
        let calendar = Calendar.current
        
        for i in 0..<selectedTimeRange.days {
            let date = calendar.date(byAdding: .day, value: -i, to: endDate) ?? endDate
            let dayStart = calendar.startOfDay(for: date)
            let dayEnd = calendar.date(byAdding: .day, value: 1, to: dayStart) ?? dayStart
            
            let dayEvents = filteredEvents.filter { event in
                event.timestamp >= dayStart && event.timestamp < dayEnd
            }
            
            dailyEventCounts.append(dayEvents.count)
        }
        
        let peakDayEvents = dailyEventCounts.max() ?? 0
        
        return Statistics(
            totalEvents: totalEvents,
            averagePerDay: averagePerDay,
            averageHeartRate: averageHeartRate,
            peakDayEvents: peakDayEvents
        )
    }
    
    private func getRecentEvents() -> [SmokingEvent] {
        let endDate = selectedDate
        let startDate = Calendar.current.date(byAdding: .day, value: -selectedTimeRange.days, to: endDate) ?? endDate
        
        return appState.smokingEvents
            .filter { event in
                event.timestamp >= startDate && event.timestamp <= endDate
            }
            .sorted { $0.timestamp > $1.timestamp }
            .prefix(10)
            .map { $0 }
    }
}

// MARK: - Supporting Types and Views

struct ChartDataPoint {
    let date: Date
    let count: Int
    let heartRate: Double
}

struct HeartRateChartDataPoint {
    let date: Date
    let heartRate: Double
}

struct Statistics {
    let totalEvents: Int
    let averagePerDay: Double
    let averageHeartRate: Double
    let peakDayEvents: Int
}

struct StatisticCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
}

struct EventRow: View {
    let event: SmokingEvent
    
    var body: some View {
        HStack {
            Image(systemName: "smoke.fill")
                .foregroundColor(.orange)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(event.timestamp.formatted(date: .abbreviated, time: .shortened))
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("Confidence: \(Int(event.confidence * 100))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            if let heartRate = event.heartRateAtEvent {
                VStack(alignment: .trailing, spacing: 2) {
                    Text("\(Int(heartRate))")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    Text("BPM")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
}

#Preview {
    HistoryView(appState: AppState())
}
