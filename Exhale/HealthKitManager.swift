//
//  HealthKitManager.swift
//  Exhale
//
//  Created by <PERSON> on 02.07.25.
//

import Foundation
import HealthKit
import Combine

// MARK: - HealthKitManager Protocol

protocol HealthKitManagerProtocol: ObservableObject {
    var heartRate: Double { get }
    var isAuthorized: Bool { get }
    var authorizationError: String? { get }

    func requestAuthorization()
    func startHeartRateMonitoring()
    func stopHeartRateMonitoring()
    func fetchHeartRateHistory(from startDate: Date, to endDate: Date, completion: @escaping ([HKQuantitySample]) -> Void)
}

class HealthKitManager: ObservableObject, HealthKitManagerProtocol {
    private let healthStore = HKHealthStore()
    
    @Published var heartRate: Double = 0.0
    @Published var isAuthorized = false
    @Published var authorizationError: String?
    
    private var heartRateQuery: HKAnchoredObjectQuery?
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        // Initialize without system service access
        // System services will be initialized when needed
    }

    private func initializeHealthKitIfNeeded() {
        // Only initialize HealthKit when actually needed and not in test environment
        guard !EnvironmentDetector.shared.isTestEnvironment else { return }
        checkAuthorizationStatus()
    }
    
    // MARK: - Authorization
    
    func requestAuthorization() {
        initializeHealthKitIfNeeded()

        guard HKHealthStore.isHealthDataAvailable() else {
            authorizationError = "HealthKit is not available on this device"
            return
        }
        
        let heartRateType = HKQuantityType.quantityType(forIdentifier: .heartRate)!
        let typesToRead: Set<HKObjectType> = [heartRateType]
        
        healthStore.requestAuthorization(toShare: nil, read: typesToRead) { [weak self] success, error in
            DispatchQueue.main.async {
                if success {
                    self?.isAuthorized = true
                    self?.authorizationError = nil
                    self?.startHeartRateMonitoring()
                } else {
                    self?.isAuthorized = false
                    self?.authorizationError = error?.localizedDescription ?? "Authorization failed"
                }
            }
        }
    }
    
    private func checkAuthorizationStatus() {
        let heartRateType = HKQuantityType.quantityType(forIdentifier: .heartRate)!
        let status = healthStore.authorizationStatus(for: heartRateType)
        
        DispatchQueue.main.async {
            switch status {
            case .sharingAuthorized:
                self.isAuthorized = true
                self.startHeartRateMonitoring()
            case .notDetermined:
                self.isAuthorized = false
            case .sharingDenied:
                self.isAuthorized = false
                self.authorizationError = "HealthKit access denied"
            @unknown default:
                self.isAuthorized = false
                self.authorizationError = "Unknown authorization status"
            }
        }
    }
    
    // MARK: - Heart Rate Monitoring
    
    func startHeartRateMonitoring() {
        initializeHealthKitIfNeeded()
        guard isAuthorized else { return }
        
        let heartRateType = HKQuantityType.quantityType(forIdentifier: .heartRate)!
        
        // Create a predicate to get the most recent heart rate data
        let predicate = HKQuery.predicateForSamples(
            withStart: Date().addingTimeInterval(-3600), // Last hour
            end: Date(),
            options: .strictEndDate
        )
        
        // Create an anchored object query for real-time updates
        heartRateQuery = HKAnchoredObjectQuery(
            type: heartRateType,
            predicate: predicate,
            anchor: nil,
            limit: HKObjectQueryNoLimit
        ) { [weak self] query, samples, deletedObjects, anchor, error in
            
            if let error = error {
                DispatchQueue.main.async {
                    self?.authorizationError = "Heart rate query error: \(error.localizedDescription)"
                }
                return
            }
            
            self?.processHeartRateSamples(samples)
        }
        
        // Set up the update handler for real-time monitoring
        heartRateQuery?.updateHandler = { [weak self] query, samples, deletedObjects, anchor, error in
            if let error = error {
                DispatchQueue.main.async {
                    self?.authorizationError = "Heart rate update error: \(error.localizedDescription)"
                }
                return
            }
            
            self?.processHeartRateSamples(samples)
        }
        
        healthStore.execute(heartRateQuery!)
    }
    
    private func processHeartRateSamples(_ samples: [HKSample]?) {
        guard let heartRateSamples = samples as? [HKQuantitySample] else { return }
        
        // Get the most recent heart rate sample
        if let latestSample = heartRateSamples.sorted(by: { $0.endDate > $1.endDate }).first {
            let heartRateUnit = HKUnit.count().unitDivided(by: HKUnit.minute())
            let heartRateValue = latestSample.quantity.doubleValue(for: heartRateUnit)
            
            DispatchQueue.main.async {
                self.heartRate = heartRateValue
            }
        }
    }
    
    func stopHeartRateMonitoring() {
        if let query = heartRateQuery {
            healthStore.stop(query)
            heartRateQuery = nil
        }
    }
    
    // MARK: - Heart Rate History
    
    func fetchHeartRateHistory(from startDate: Date, to endDate: Date, completion: @escaping ([HKQuantitySample]) -> Void) {
        let heartRateType = HKQuantityType.quantityType(forIdentifier: .heartRate)!
        let predicate = HKQuery.predicateForSamples(withStart: startDate, end: endDate, options: .strictStartDate)
        
        let query = HKSampleQuery(
            sampleType: heartRateType,
            predicate: predicate,
            limit: HKObjectQueryNoLimit,
            sortDescriptors: [NSSortDescriptor(key: HKSampleSortIdentifierEndDate, ascending: true)]
        ) { query, samples, error in
            
            if let error = error {
                print("Error fetching heart rate history: \(error.localizedDescription)")
                completion([])
                return
            }
            
            let heartRateSamples = samples as? [HKQuantitySample] ?? []
            completion(heartRateSamples)
        }
        
        healthStore.execute(query)
    }
    
    deinit {
        stopHeartRateMonitoring()
    }
}
