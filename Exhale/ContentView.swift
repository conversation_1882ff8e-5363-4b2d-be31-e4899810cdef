//
//  ContentView.swift
//  Exhale
//
//  Created by <PERSON> on 02.07.25.
//

import SwiftUI

struct ContentView: View {
    @Environment(\.dependencyContainer) private var dependencyContainer
    @StateObject private var healthKitManager: HealthKitManagerWrapper
    @StateObject private var motionManager: MotionDetectionManagerWrapper
    @StateObject private var notificationManager: NotificationManagerWrapper
    @StateObject private var appState = AppState()

    init() {
        let container = DependencyContainer.shared
        let healthManager = HealthKitManagerWrapper(manager: container.healthKitManager())
        let motionManager = MotionDetectionManagerWrapper(manager: container.motionDetectionManager())
        let notificationManager = NotificationManagerWrapper(manager: container.notificationManager())

        self._healthKitManager = StateObject(wrappedValue: healthManager)
        self._motionManager = StateObject(wrappedValue: motionManager)
        self._notificationManager = StateObject(wrappedValue: notificationManager)
    }

    @State private var selectedTab = 0

    var body: some View {
        TabView(selection: $selectedTab) {
            // Dashboard Tab
            DashboardView(
                healthKitManager: healthKitManager,
                motionManager: motionManager,
                appState: appState
            )
            .tabItem {
                Image(systemName: "heart.fill")
                Text("Dashboard")
            }
            .tag(0)

            // History Tab
            HistoryView(appState: appState)
            .tabItem {
                Image(systemName: "chart.line.uptrend.xyaxis")
                Text("History")
            }
            .tag(1)

            // Settings Tab
            SettingsView(
                healthKitManager: healthKitManager,
                motionManager: motionManager,
                appState: appState
            )
            .tabItem {
                Image(systemName: "gearshape.fill")
                Text("Settings")
            }
            .tag(2)
        }
        .onAppear {
            appState.loadData()
            setupNotifications()
        }
    }

    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            forName: .smokingDetected,
            object: nil,
            queue: .main
        ) { _ in
            handleSmokingDetection()
        }
    }

    private func handleSmokingDetection() {
        let smokingEvent = SmokingEvent(
            confidence: motionManager.smokingConfidence,
            heartRateAtEvent: healthKitManager.heartRate > 0 ? healthKitManager.heartRate : nil
        )

        appState.addSmokingEvent(smokingEvent)

        // Send notification about smoking detection
        notificationManager.sendSmokingDetectedNotification(
            confidence: motionManager.smokingConfidence,
            heartRate: healthKitManager.heartRate > 0 ? healthKitManager.heartRate : nil
        )

        // Add corresponding heart rate reading if available
        if healthKitManager.heartRate > 0 {
            let heartRateReading = HeartRateReading(
                heartRate: healthKitManager.heartRate,
                isSmokingRelated: true
            )
            appState.addHeartRateReading(heartRateReading)
        }
    }
}

#Preview {
    ContentView()
}
