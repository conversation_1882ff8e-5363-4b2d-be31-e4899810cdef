//
//  MotionDetectionManager.swift
//  Exhale
//
//  Created by <PERSON> on 02.07.25.
//

import Foundation
import CoreMotion
import Combine

class MotionDetectionManager: ObservableObject {
    private let motionManager = CMMotionManager()
    private let operationQueue = OperationQueue()
    
    @Published var isMonitoring = false
    @Published var smokingDetected = false
    @Published var lastSmokingEvent: Date?
    @Published var smokingConfidence: Double = 0.0
    
    // Motion data storage for pattern analysis
    private var accelerometerData: [CMAccelerometerData] = []
    private var gyroscopeData: [CMGyroData] = []
    private var motionBuffer: [MotionSample] = []
    
    // Detection parameters
    private let bufferSize = 100 // Number of samples to analyze
    private let updateInterval = 0.1 // 10 Hz sampling rate
    private let smokingThreshold = 0.7 // Confidence threshold for smoking detection
    
    struct MotionSample {
        let timestamp: Date
        let acceleration: CMAcceleration
        let rotationRate: CMRotationRate
    }
    
    init() {
        setupMotionManager()
    }
    
    private func setupMotionManager() {
        motionManager.accelerometerUpdateInterval = updateInterval
        motionManager.gyroUpdateInterval = updateInterval
        
        operationQueue.maxConcurrentOperationCount = 1
        operationQueue.name = "MotionDetectionQueue"
    }
    
    // MARK: - Motion Monitoring
    
    func startMotionMonitoring() {
        guard motionManager.isAccelerometerAvailable && motionManager.isGyroAvailable else {
            print("Motion sensors not available")
            return
        }
        
        isMonitoring = true
        smokingDetected = false
        
        // Start accelerometer updates
        motionManager.startAccelerometerUpdates(to: operationQueue) { [weak self] data, error in
            if let error = error {
                print("Accelerometer error: \(error.localizedDescription)")
                return
            }
            
            if let accelerometerData = data {
                self?.processAccelerometerData(accelerometerData)
            }
        }
        
        // Start gyroscope updates
        motionManager.startGyroUpdates(to: operationQueue) { [weak self] data, error in
            if let error = error {
                print("Gyroscope error: \(error.localizedDescription)")
                return
            }
            
            if let gyroData = data {
                self?.processGyroscopeData(gyroData)
            }
        }
    }
    
    func stopMotionMonitoring() {
        motionManager.stopAccelerometerUpdates()
        motionManager.stopGyroUpdates()
        isMonitoring = false
        clearMotionBuffer()
    }
    
    // MARK: - Data Processing
    
    private func processAccelerometerData(_ data: CMAccelerometerData) {
        accelerometerData.append(data)
        
        // Keep only recent data (last 10 seconds)
        let cutoffTime = Date().addingTimeInterval(-10)
        accelerometerData.removeAll { $0.timestamp < cutoffTime }
        
        updateMotionBuffer(acceleration: data.acceleration, timestamp: Date())
    }
    
    private func processGyroscopeData(_ data: CMGyroData) {
        gyroscopeData.append(data)
        
        // Keep only recent data (last 10 seconds)
        let cutoffTime = Date().addingTimeInterval(-10)
        gyroscopeData.removeAll { $0.timestamp < cutoffTime }
    }
    
    private func updateMotionBuffer(acceleration: CMAcceleration, timestamp: Date) {
        // Find corresponding gyroscope data (within 50ms)
        let matchingGyroData = gyroscopeData.first { gyroData in
            abs(gyroData.timestamp.timeIntervalSince(timestamp)) < 0.05
        }
        
        let rotationRate = matchingGyroData?.rotationRate ?? CMRotationRate(x: 0, y: 0, z: 0)
        
        let sample = MotionSample(
            timestamp: timestamp,
            acceleration: acceleration,
            rotationRate: rotationRate
        )
        
        motionBuffer.append(sample)
        
        // Maintain buffer size
        if motionBuffer.count > bufferSize {
            motionBuffer.removeFirst()
        }
        
        // Analyze motion pattern if buffer is full
        if motionBuffer.count >= bufferSize {
            analyzeSmokingPattern()
        }
    }
    
    // MARK: - Smoking Detection Algorithm
    
    private func analyzeSmokingPattern() {
        guard motionBuffer.count >= bufferSize else { return }
        
        let confidence = detectSmokingGesture()
        
        DispatchQueue.main.async {
            self.smokingConfidence = confidence
            
            if confidence > self.smokingThreshold {
                self.triggerSmokingDetection()
            }
        }
    }
    
    private func detectSmokingGesture() -> Double {
        // Smoking gesture characteristics:
        // 1. Repetitive hand-to-mouth movements
        // 2. Specific acceleration patterns (lifting, bringing to mouth, lowering)
        // 3. Rotational movements of the wrist
        // 4. Periodic nature of the movements
        
        var confidence: Double = 0.0
        
        // Analyze vertical movement patterns (Y-axis acceleration)
        let verticalMovements = analyzeVerticalMovements()
        confidence += verticalMovements * 0.4
        
        // Analyze rotational patterns
        let rotationalPatterns = analyzeRotationalPatterns()
        confidence += rotationalPatterns * 0.3
        
        // Analyze periodicity
        let periodicity = analyzePeriodicity()
        confidence += periodicity * 0.3
        
        return min(confidence, 1.0)
    }
    
    private func analyzeVerticalMovements() -> Double {
        let yAccelerations = motionBuffer.map { $0.acceleration.y }
        
        // Look for alternating up/down movements
        var peaks = 0
        var valleys = 0
        
        for i in 1..<yAccelerations.count - 1 {
            let prev = yAccelerations[i - 1]
            let current = yAccelerations[i]
            let next = yAccelerations[i + 1]
            
            // Peak detection
            if current > prev && current > next && current > 0.2 {
                peaks += 1
            }
            
            // Valley detection
            if current < prev && current < next && current < -0.2 {
                valleys += 1
            }
        }
        
        // Smoking typically has 2-4 hand-to-mouth movements in a 10-second window
        let expectedMovements = 3
        let actualMovements = min(peaks, valleys)
        
        if actualMovements == 0 { return 0.0 }
        
        return 1.0 - abs(Double(actualMovements - expectedMovements)) / Double(expectedMovements)
    }
    
    private func analyzeRotationalPatterns() -> Double {
        let rotationMagnitudes = motionBuffer.map { sample in
            sqrt(pow(sample.rotationRate.x, 2) + pow(sample.rotationRate.y, 2) + pow(sample.rotationRate.z, 2))
        }
        
        let averageRotation = rotationMagnitudes.reduce(0, +) / Double(rotationMagnitudes.count)
        
        // Smoking involves moderate wrist rotation (not too high, not too low)
        let optimalRotation = 1.5 // radians per second
        let rotationScore = 1.0 - abs(averageRotation - optimalRotation) / optimalRotation
        
        return max(0.0, rotationScore)
    }
    
    private func analyzePeriodicity() -> Double {
        // Simple periodicity analysis based on acceleration variance
        let accelerationMagnitudes = motionBuffer.map { sample in
            sqrt(pow(sample.acceleration.x, 2) + pow(sample.acceleration.y, 2) + pow(sample.acceleration.z, 2))
        }
        
        let mean = accelerationMagnitudes.reduce(0, +) / Double(accelerationMagnitudes.count)
        let variance = accelerationMagnitudes.map { pow($0 - mean, 2) }.reduce(0, +) / Double(accelerationMagnitudes.count)
        
        // Smoking has moderate variance (repetitive but not constant)
        let optimalVariance = 0.5
        let varianceScore = 1.0 - abs(variance - optimalVariance) / optimalVariance
        
        return max(0.0, min(1.0, varianceScore))
    }
    
    private func triggerSmokingDetection() {
        smokingDetected = true
        lastSmokingEvent = Date()
        
        // Reset detection state after a brief period
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.smokingDetected = false
        }
        
        // Post notification for other parts of the app
        NotificationCenter.default.post(name: .smokingDetected, object: nil)
    }
    
    private func clearMotionBuffer() {
        motionBuffer.removeAll()
        accelerometerData.removeAll()
        gyroscopeData.removeAll()
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let smokingDetected = Notification.Name("smokingDetected")
}
