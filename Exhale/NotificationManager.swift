//
//  NotificationManager.swift
//  Exhale
//
//  Created by <PERSON> on 02.07.25.
//

import Foundation
import UserNotifications
import UIKit

// MARK: - NotificationManager Protocol

protocol NotificationManagerProtocol: ObservableObject {
    func sendSmokingDetectedNotification(confidence: Double, heartRate: Double?)
    func sendHeartRateAlertNotification(heartRate: Double, threshold: Double)
    func sendDailySummaryNotification(smokingEvents: Int, averageHeartRate: Double?)
    func sendGoalAchievementNotification(goalType: String, achievement: String)
    func scheduleReminderNotification(title: String, body: String, timeInterval: TimeInterval)
    func requestNotificationPermission(completion: @escaping (Bool) -> Void)
    func clearAllNotifications()
    func checkNotificationPermissions(completion: @escaping (Bool) -> Void)
}

class NotificationManager: NSObject, ObservableObject, NotificationManagerProtocol {
    static let shared = NotificationManager()
    
    private override init() {
        super.init()
        // Initialize without system service access
        // Notification center will be initialized when needed
    }

    private func initializeNotificationCenterIfNeeded() {
        // Only initialize notification center when actually needed and not in test environment
        guard !EnvironmentDetector.shared.isTestEnvironment else { return }
        setupNotificationCenter()
    }
    
    private func setupNotificationCenter() {
        UNUserNotificationCenter.current().delegate = self
    }
    
    // MARK: - Smoking Detection Notifications
    
    func sendSmokingDetectedNotification(confidence: Double, heartRate: Double?) {
        initializeNotificationCenterIfNeeded()

        let content = UNMutableNotificationContent()
        content.title = "Smoking Event Detected"
        
        if let heartRate = heartRate {
            content.body = "Detected with \(Int(confidence * 100))% confidence. Heart rate: \(Int(heartRate)) BPM"
        } else {
            content.body = "Detected with \(Int(confidence * 100))% confidence."
        }
        
        content.sound = .default
        content.badge = 1
        content.categoryIdentifier = "SMOKING_DETECTION"
        
        // Add custom data
        content.userInfo = [
            "type": "smoking_detection",
            "confidence": confidence,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        if let heartRate = heartRate {
            content.userInfo["heartRate"] = heartRate
        }
        
        let request = UNNotificationRequest(
            identifier: "smoking_\(UUID().uuidString)",
            content: content,
            trigger: nil // Immediate notification
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Error sending smoking detection notification: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - Reminder Notifications

    func scheduleReminderNotification(title: String, body: String, timeInterval: TimeInterval) {
        initializeNotificationCenterIfNeeded()

        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.sound = .default
        content.categoryIdentifier = "REMINDER"

        content.userInfo = [
            "type": "reminder",
            "scheduledFor": Date().addingTimeInterval(timeInterval).timeIntervalSince1970,
            "timestamp": Date().timeIntervalSince1970
        ]

        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: timeInterval, repeats: false)

        let request = UNNotificationRequest(
            identifier: "reminder_\(UUID().uuidString)",
            content: content,
            trigger: trigger
        )

        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Error scheduling reminder notification: \(error.localizedDescription)")
            }
        }
    }

    func scheduleReminderNotification(interval: TimeInterval) {
        let content = UNMutableNotificationContent()
        content.title = "Health Check Reminder"
        content.body = "How are you feeling? Remember to stay mindful of your smoking habits."
        content.sound = .default
        content.categoryIdentifier = "REMINDER"
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: interval, repeats: true)
        
        let request = UNNotificationRequest(
            identifier: "reminder_notification",
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Error scheduling reminder notification: \(error.localizedDescription)")
            }
        }
    }
    
    func cancelReminderNotifications() {
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: ["reminder_notification"])
    }
    
    // MARK: - Heart Rate Alerts
    
    func sendHeartRateAlertNotification(heartRate: Double, threshold: Double) {
        let isHigh = heartRate > threshold
        let content = UNMutableNotificationContent()
        content.title = isHigh ? "High Heart Rate Alert" : "Low Heart Rate Alert"
        content.body = "Your heart rate is \(Int(heartRate)) BPM. Consider taking a break."
        content.sound = .default
        content.badge = 1
        content.categoryIdentifier = "HEART_RATE_ALERT"
        
        content.userInfo = [
            "type": "heart_rate_alert",
            "heartRate": heartRate,
            "isHigh": isHigh,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        let request = UNNotificationRequest(
            identifier: "heart_rate_\(UUID().uuidString)",
            content: content,
            trigger: nil
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Error sending heart rate alert: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - Daily Summary Notifications

    func sendDailySummaryNotification(smokingEvents: Int, averageHeartRate: Double?) {
        initializeNotificationCenterIfNeeded()

        let content = UNMutableNotificationContent()
        content.title = "Daily Summary"

        if let avgHeartRate = averageHeartRate {
            content.body = "Today: \(smokingEvents) smoking events detected. Average heart rate: \(Int(avgHeartRate)) BPM"
        } else {
            content.body = "Today: \(smokingEvents) smoking events detected."
        }

        content.sound = .default
        content.badge = 1
        content.categoryIdentifier = "DAILY_SUMMARY"

        content.userInfo = [
            "type": "daily_summary",
            "smokingEvents": smokingEvents,
            "averageHeartRate": averageHeartRate as Any,
            "timestamp": Date().timeIntervalSince1970
        ]

        let request = UNNotificationRequest(
            identifier: "daily_summary_\(UUID().uuidString)",
            content: content,
            trigger: nil
        )

        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Error sending daily summary notification: \(error.localizedDescription)")
            }
        }
    }

    func scheduleDailySummaryNotification() {
        let content = UNMutableNotificationContent()
        content.title = "Daily Summary"
        content.body = "Check your daily smoking and heart rate summary."
        content.sound = .default
        content.categoryIdentifier = "DAILY_SUMMARY"
        
        // Schedule for 9 PM every day
        var dateComponents = DateComponents()
        dateComponents.hour = 21
        dateComponents.minute = 0
        
        let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: true)
        
        let request = UNNotificationRequest(
            identifier: "daily_summary",
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Error scheduling daily summary notification: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - Goal Achievement Notifications
    
    func sendGoalAchievementNotification(goalType: String, achievement: String) {
        let content = UNMutableNotificationContent()
        
        content.title = "Goal Achievement"
        content.body = "\(goalType): \(achievement)"
        
        content.sound = .default
        content.badge = 1
        content.categoryIdentifier = "GOAL_ACHIEVEMENT"
        
        content.userInfo = [
            "type": "goal_achievement",
            "goalType": goalType,
            "achievement": achievement,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        let request = UNNotificationRequest(
            identifier: "goal_\(UUID().uuidString)",
            content: content,
            trigger: nil
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Error sending goal achievement notification: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - Notification Categories
    
    func setupNotificationCategories() {
        // Smoking Detection Actions
        let viewDetailsAction = UNNotificationAction(
            identifier: "VIEW_DETAILS",
            title: "View Details",
            options: [.foreground]
        )
        
        let dismissAction = UNNotificationAction(
            identifier: "DISMISS",
            title: "Dismiss",
            options: []
        )
        
        let smokingCategory = UNNotificationCategory(
            identifier: "SMOKING_DETECTION",
            actions: [viewDetailsAction, dismissAction],
            intentIdentifiers: [],
            options: []
        )
        
        // Reminder Actions
        let checkInAction = UNNotificationAction(
            identifier: "CHECK_IN",
            title: "Check In",
            options: [.foreground]
        )
        
        let reminderCategory = UNNotificationCategory(
            identifier: "REMINDER",
            actions: [checkInAction, dismissAction],
            intentIdentifiers: [],
            options: []
        )
        
        // Heart Rate Alert Actions
        let viewHeartRateAction = UNNotificationAction(
            identifier: "VIEW_HEART_RATE",
            title: "View Heart Rate",
            options: [.foreground]
        )
        
        let heartRateCategory = UNNotificationCategory(
            identifier: "HEART_RATE_ALERT",
            actions: [viewHeartRateAction, dismissAction],
            intentIdentifiers: [],
            options: []
        )
        
        // Daily Summary Actions
        let viewSummaryAction = UNNotificationAction(
            identifier: "VIEW_SUMMARY",
            title: "View Summary",
            options: [.foreground]
        )
        
        let summaryCategory = UNNotificationCategory(
            identifier: "DAILY_SUMMARY",
            actions: [viewSummaryAction, dismissAction],
            intentIdentifiers: [],
            options: []
        )
        
        // Goal Achievement Actions
        let viewProgressAction = UNNotificationAction(
            identifier: "VIEW_PROGRESS",
            title: "View Progress",
            options: [.foreground]
        )
        
        let goalCategory = UNNotificationCategory(
            identifier: "GOAL_ACHIEVEMENT",
            actions: [viewProgressAction, dismissAction],
            intentIdentifiers: [],
            options: []
        )
        
        UNUserNotificationCenter.current().setNotificationCategories([
            smokingCategory,
            reminderCategory,
            heartRateCategory,
            summaryCategory,
            goalCategory
        ])
    }
    
    // MARK: - Utility Methods

    func requestNotificationPermission(completion: @escaping (Bool) -> Void) {
        initializeNotificationCenterIfNeeded()

        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            DispatchQueue.main.async {
                completion(granted)
            }
        }
    }

    func clearAllNotifications() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
        UNUserNotificationCenter.current().removeAllDeliveredNotifications()
        if #available(iOS 16.0, *) {
            UNUserNotificationCenter.current().setBadgeCount(0)
        } else {
            UIApplication.shared.applicationIconBadgeNumber = 0
        }
    }

    func checkNotificationPermissions(completion: @escaping (Bool) -> Void) {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                completion(settings.authorizationStatus == .authorized)
            }
        }
    }
}

// MARK: - UNUserNotificationCenterDelegate

extension NotificationManager: UNUserNotificationCenterDelegate {
    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        
        let userInfo = response.notification.request.content.userInfo
        
        switch response.actionIdentifier {
        case "VIEW_DETAILS", "VIEW_HEART_RATE", "VIEW_SUMMARY", "VIEW_PROGRESS":
            // Handle opening the app to specific views
            NotificationCenter.default.post(name: .openAppFromNotification, object: userInfo)
            
        case "CHECK_IN":
            // Handle check-in action
            NotificationCenter.default.post(name: .checkInFromNotification, object: nil)
            
        case "DISMISS":
            // Just dismiss the notification
            break
            
        default:
            // Default action (tap on notification)
            NotificationCenter.default.post(name: .openAppFromNotification, object: userInfo)
        }
        
        completionHandler()
    }
    
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        // Show notification even when app is in foreground
        completionHandler([.banner, .sound, .badge])
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let openAppFromNotification = Notification.Name("openAppFromNotification")
    static let checkInFromNotification = Notification.Name("checkInFromNotification")
}
