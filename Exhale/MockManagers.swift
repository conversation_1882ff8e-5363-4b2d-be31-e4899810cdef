//
//  MockManagers.swift
//  Exhale
//
//  Created by <PERSON> on 02.07.25.
//

import Foundation
import HealthKit
import Combine

// MARK: - Mock HealthKit Manager

class MockHealthKitManager: ObservableObject, HealthKitManagerProtocol {
    @Published var heartRate: Double = 72.0
    @Published var isAuthorized: Bool = true
    @Published var authorizationError: String? = nil
    
    private var timer: Timer?
    
    init() {
        // Start simulating heart rate changes
        startSimulatedHeartRate()
    }
    
    func requestAuthorization() {
        // Simulate successful authorization
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.isAuthorized = true
            self.authorizationError = nil
        }
    }
    
    func startHeartRateMonitoring() {
        startSimulatedHeartRate()
    }
    
    func stopHeartRateMonitoring() {
        timer?.invalidate()
        timer = nil
    }
    
    func fetchHeartRateHistory(from startDate: Date, to endDate: Date, completion: @escaping ([HKQuantitySample]) -> Void) {
        // Return empty array for mock - tests can override this behavior if needed
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            completion([])
        }
    }
    
    private func startSimulatedHeartRate() {
        timer?.invalidate()
        timer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { _ in
            // Simulate realistic heart rate variations (60-100 BPM)
            let baseRate = 72.0
            let variation = Double.random(in: -12.0...28.0)
            self.heartRate = max(60.0, min(100.0, baseRate + variation))
        }
    }
    
    deinit {
        timer?.invalidate()
    }
}

// MARK: - Mock Motion Detection Manager

class MockMotionDetectionManager: ObservableObject, MotionDetectionManagerProtocol {
    @Published var isMonitoring: Bool = false
    @Published var smokingDetected: Bool = false
    @Published var lastSmokingEvent: Date? = nil
    @Published var smokingConfidence: Double = 0.0
    
    private var simulationTimer: Timer?
    
    func startMotionMonitoring() {
        isMonitoring = true
        startSimulatedDetection()
    }
    
    func stopMotionMonitoring() {
        isMonitoring = false
        simulationTimer?.invalidate()
        simulationTimer = nil
    }
    
    private func startSimulatedDetection() {
        simulationTimer?.invalidate()
        simulationTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { _ in
            // Simulate occasional smoking detection for testing
            let shouldDetect = Double.random(in: 0...1) < 0.1 // 10% chance every 5 seconds
            
            if shouldDetect {
                self.smokingConfidence = Double.random(in: 0.7...0.95)
                self.triggerMockSmokingDetection()
            } else {
                self.smokingConfidence = Double.random(in: 0.0...0.6)
            }
        }
    }
    
    private func triggerMockSmokingDetection() {
        smokingDetected = true
        lastSmokingEvent = Date()
        
        // Reset detection state after a brief period
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.smokingDetected = false
        }
        
        // Post notification for other parts of the app
        NotificationCenter.default.post(name: .smokingDetected, object: nil)
    }
    
    deinit {
        simulationTimer?.invalidate()
    }
}

// MARK: - Mock Notification Manager

class MockNotificationManager: ObservableObject, NotificationManagerProtocol {
    
    // Track notifications for testing purposes
    private(set) var sentNotifications: [MockNotification] = []
    
    struct MockNotification {
        let type: String
        let title: String
        let body: String
        let timestamp: Date
        let userInfo: [String: Any]
    }
    
    func sendSmokingDetectedNotification(confidence: Double, heartRate: Double?) {
        let title = "Smoking Event Detected"
        let body: String
        
        if let heartRate = heartRate {
            body = "Detected with \(Int(confidence * 100))% confidence. Heart rate: \(Int(heartRate)) BPM"
        } else {
            body = "Detected with \(Int(confidence * 100))% confidence."
        }
        
        var userInfo: [String: Any] = [
            "type": "smoking_detection",
            "confidence": confidence,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        if let heartRate = heartRate {
            userInfo["heartRate"] = heartRate
        }
        
        let notification = MockNotification(
            type: "smoking_detection",
            title: title,
            body: body,
            timestamp: Date(),
            userInfo: userInfo
        )
        
        sentNotifications.append(notification)
        print("Mock Notification: \(title) - \(body)")
    }
    
    func sendHeartRateAlertNotification(heartRate: Double, threshold: Double) {
        let title = "Heart Rate Alert"
        let body = "Heart rate is \(Int(heartRate)) BPM (threshold: \(Int(threshold)) BPM)"
        
        let notification = MockNotification(
            type: "heart_rate_alert",
            title: title,
            body: body,
            timestamp: Date(),
            userInfo: [
                "type": "heart_rate_alert",
                "heartRate": heartRate,
                "threshold": threshold,
                "timestamp": Date().timeIntervalSince1970
            ]
        )
        
        sentNotifications.append(notification)
        print("Mock Notification: \(title) - \(body)")
    }
    
    func sendDailySummaryNotification(smokingEvents: Int, averageHeartRate: Double?) {
        let title = "Daily Summary"
        let body: String
        
        if let avgHeartRate = averageHeartRate {
            body = "Today: \(smokingEvents) smoking events detected. Average heart rate: \(Int(avgHeartRate)) BPM"
        } else {
            body = "Today: \(smokingEvents) smoking events detected."
        }
        
        let notification = MockNotification(
            type: "daily_summary",
            title: title,
            body: body,
            timestamp: Date(),
            userInfo: [
                "type": "daily_summary",
                "smokingEvents": smokingEvents,
                "averageHeartRate": averageHeartRate as Any,
                "timestamp": Date().timeIntervalSince1970
            ]
        )
        
        sentNotifications.append(notification)
        print("Mock Notification: \(title) - \(body)")
    }
    
    func sendGoalAchievementNotification(goalType: String, achievement: String) {
        let title = "Goal Achievement"
        let body = "\(goalType): \(achievement)"
        
        let notification = MockNotification(
            type: "goal_achievement",
            title: title,
            body: body,
            timestamp: Date(),
            userInfo: [
                "type": "goal_achievement",
                "goalType": goalType,
                "achievement": achievement,
                "timestamp": Date().timeIntervalSince1970
            ]
        )
        
        sentNotifications.append(notification)
        print("Mock Notification: \(title) - \(body)")
    }
    
    func scheduleReminderNotification(title: String, body: String, timeInterval: TimeInterval) {
        let notification = MockNotification(
            type: "reminder",
            title: title,
            body: body,
            timestamp: Date().addingTimeInterval(timeInterval),
            userInfo: [
                "type": "reminder",
                "scheduledFor": Date().addingTimeInterval(timeInterval).timeIntervalSince1970,
                "timestamp": Date().timeIntervalSince1970
            ]
        )
        
        sentNotifications.append(notification)
        print("Mock Scheduled Notification: \(title) - \(body) (in \(timeInterval)s)")
    }
    
    func requestNotificationPermission(completion: @escaping (Bool) -> Void) {
        // Simulate successful permission grant
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            completion(true)
        }
    }
    
    func clearAllNotifications() {
        sentNotifications.removeAll()
        print("Mock: All notifications cleared")
    }
    
    func checkNotificationPermissions(completion: @escaping (Bool) -> Void) {
        // Always return true for mock
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            completion(true)
        }
    }
    
    // Test helper methods
    func getNotificationCount(ofType type: String) -> Int {
        return sentNotifications.filter { $0.type == type }.count
    }
    
    func getLastNotification(ofType type: String) -> MockNotification? {
        return sentNotifications.filter { $0.type == type }.last
    }
}
