//
//  SettingsView.swift
//  Exhale
//
//  Created by <PERSON> on 02.07.25.
//

import SwiftUI

struct SettingsView: View {
    @ObservedObject var healthKitManager: HealthKitManager
    @ObservedObject var motionManager: MotionDetectionManager
    @ObservedObject var appState: AppState
    
    @State private var showingResetAlert = false
    @State private var showingPermissionAlert = false
    
    var body: some View {
        NavigationView {
            Form {
                // Monitoring Settings
                monitoringSection
                
                // Detection Settings
                detectionSection
                
                // Notification Settings
                notificationSection
                
                // Privacy Settings
                privacySection
                
                // Data Management
                dataManagementSection
                
                // About Section
                aboutSection
            }
            .navigationTitle("Settings")
        }
    }
    
    private var monitoringSection: some View {
        Section("Monitoring") {
            HStack {
                Image(systemName: "heart.fill")
                    .foregroundColor(.red)
                    .frame(width: 25)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Heart Rate Monitoring")
                        .font(.subheadline)
                    
                    Text(healthKitManager.isA<PERSON>orized ? "Authorized" : "Not Authorized")
                        .font(.caption)
                        .foregroundColor(healthKitManager.isAuthorized ? .green : .red)
                }
                
                Spacer()
                
                Toggle("", isOn: .constant(healthKitManager.isAuthorized))
                    .disabled(true)
            }
            
            HStack {
                Image(systemName: "figure.walk")
                    .foregroundColor(.blue)
                    .frame(width: 25)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Motion Detection")
                        .font(.subheadline)
                    
                    Text(motionManager.isMonitoring ? "Active" : "Inactive")
                        .font(.caption)
                        .foregroundColor(motionManager.isMonitoring ? .green : .gray)
                }
                
                Spacer()
                
                Toggle("", isOn: Binding(
                    get: { motionManager.isMonitoring },
                    set: { newValue in
                        if newValue {
                            motionManager.startMotionMonitoring()
                        } else {
                            motionManager.stopMotionMonitoring()
                        }
                    }
                ))
            }
            
            if !healthKitManager.isAuthorized {
                Button("Request HealthKit Permission") {
                    healthKitManager.requestAuthorization()
                }
                .foregroundColor(.blue)
            }
        }
    }
    
    private var detectionSection: some View {
        Section("Detection Settings") {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Detection Sensitivity")
                    Spacer()
                    Text(appState.userSettings.detectionSensitivity.rawValue)
                        .foregroundColor(.secondary)
                }
                
                Picker("Detection Sensitivity", selection: $appState.userSettings.detectionSensitivity) {
                    ForEach(UserSettings.DetectionSensitivity.allCases, id: \.self) { sensitivity in
                        Text(sensitivity.rawValue).tag(sensitivity)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Confidence Threshold")
                    Spacer()
                    Text("\(Int(appState.userSettings.minimumConfidenceThreshold * 100))%")
                        .foregroundColor(.secondary)
                }
                
                Slider(
                    value: $appState.userSettings.minimumConfidenceThreshold,
                    in: 0.5...0.9,
                    step: 0.05
                ) {
                    Text("Confidence Threshold")
                } minimumValueLabel: {
                    Text("50%")
                        .font(.caption)
                } maximumValueLabel: {
                    Text("90%")
                        .font(.caption)
                }
            }
        }
    }
    
    private var notificationSection: some View {
        Section("Notifications") {
            Toggle("Enable Notifications", isOn: $appState.userSettings.notificationsEnabled)
            
            if appState.userSettings.notificationsEnabled {
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("Reminder Interval")
                        Spacer()
                        Text(formatTimeInterval(appState.userSettings.reminderInterval))
                            .foregroundColor(.secondary)
                    }
                    
                    Picker("Reminder Interval", selection: $appState.userSettings.reminderInterval) {
                        Text("15 minutes").tag(TimeInterval(900))
                        Text("30 minutes").tag(TimeInterval(1800))
                        Text("1 hour").tag(TimeInterval(3600))
                        Text("2 hours").tag(TimeInterval(7200))
                        Text("4 hours").tag(TimeInterval(14400))
                    }
                    .pickerStyle(MenuPickerStyle())
                }
            }
        }
    }
    
    private var privacySection: some View {
        Section("Privacy") {
            Toggle("Privacy Mode", isOn: $appState.userSettings.privacyMode)
            
            if appState.userSettings.privacyMode {
                Text("When enabled, sensitive data will be hidden in notifications and the app will use generic terms.")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private var dataManagementSection: some View {
        Section("Data Management") {
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Smoking Events")
                        .font(.subheadline)
                    Text("\(appState.smokingEvents.count) recorded")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                Spacer()
            }
            
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Heart Rate Readings")
                        .font(.subheadline)
                    Text("\(appState.heartRateReadings.count) recorded")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                Spacer()
            }
            
            Button("Export Data") {
                exportData()
            }
            .foregroundColor(.blue)
            
            Button("Reset All Data") {
                showingResetAlert = true
            }
            .foregroundColor(.red)
            .alert("Reset All Data", isPresented: $showingResetAlert) {
                Button("Cancel", role: .cancel) { }
                Button("Reset", role: .destructive) {
                    resetAllData()
                }
            } message: {
                Text("This will permanently delete all smoking events and heart rate data. This action cannot be undone.")
            }
        }
    }
    
    private var aboutSection: some View {
        Section("About") {
            HStack {
                Text("Version")
                Spacer()
                Text("1.0.0")
                    .foregroundColor(.secondary)
            }
            
            HStack {
                Text("Build")
                Spacer()
                Text("1")
                    .foregroundColor(.secondary)
            }
            
            Link("Privacy Policy", destination: URL(string: "https://example.com/privacy")!)
                .foregroundColor(.blue)
            
            Link("Terms of Service", destination: URL(string: "https://example.com/terms")!)
                .foregroundColor(.blue)
            
            Button("Contact Support") {
                if let url = URL(string: "mailto:<EMAIL>") {
                    UIApplication.shared.open(url)
                }
            }
            .foregroundColor(.blue)
        }
    }
    
    // MARK: - Helper Functions
    
    private func formatTimeInterval(_ interval: TimeInterval) -> String {
        let hours = Int(interval) / 3600
        let minutes = (Int(interval) % 3600) / 60
        
        if hours > 0 {
            return "\(hours) hour\(hours == 1 ? "" : "s")"
        } else {
            return "\(minutes) minute\(minutes == 1 ? "" : "s")"
        }
    }
    
    private func exportData() {
        // Create CSV data for export
        var csvContent = "Type,Timestamp,Value,Confidence,HeartRate\n"
        
        // Add smoking events
        for event in appState.smokingEvents {
            let heartRateString = event.heartRateAtEvent?.description ?? ""
            csvContent += "SmokingEvent,\(event.timestamp.ISO8601Format()),\(event.confidence),\(event.confidence),\(heartRateString)\n"
        }
        
        // Add heart rate readings
        for reading in appState.heartRateReadings {
            csvContent += "HeartRate,\(reading.timestamp.ISO8601Format()),\(reading.heartRate),,\(reading.heartRate)\n"
        }
        
        // Share the CSV data
        let activityViewController = UIActivityViewController(
            activityItems: [csvContent],
            applicationActivities: nil
        )
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityViewController, animated: true)
        }
    }
    
    private func resetAllData() {
        appState.smokingEvents.removeAll()
        appState.heartRateReadings.removeAll()
        
        // Clear UserDefaults
        UserDefaults.standard.removeObject(forKey: "smokingEvents")
        UserDefaults.standard.removeObject(forKey: "heartRateReadings")
    }
}

#Preview {
    SettingsView(
        healthKitManager: HealthKitManager(),
        motionManager: MotionDetectionManager(),
        appState: AppState()
    )
}
