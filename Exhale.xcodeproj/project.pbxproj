// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		A18F8EAF2E157214009D4427 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A18F8E962E157212009D4427 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A18F8E9D2E157212009D4427;
			remoteInfo = Exhale;
		};
		A18F8EB92E157214009D4427 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A18F8E962E157212009D4427 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A18F8E9D2E157212009D4427;
			remoteInfo = Exhale;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		A18F8E9E2E157212009D4427 /* Exhale.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Exhale.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A18F8EAE2E157214009D4427 /* ExhaleTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ExhaleTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		A18F8EB82E157214009D4427 /* ExhaleUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ExhaleUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		A18F8EA02E157212009D4427 /* Exhale */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Exhale;
			sourceTree = "<group>";
		};
		A18F8EB12E157214009D4427 /* ExhaleTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = ExhaleTests;
			sourceTree = "<group>";
		};
		A18F8EBB2E157214009D4427 /* ExhaleUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = ExhaleUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		A18F8E9B2E157212009D4427 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A18F8EAB2E157214009D4427 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A18F8EB52E157214009D4427 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A18F8E952E157212009D4427 = {
			isa = PBXGroup;
			children = (
				A18F8EA02E157212009D4427 /* Exhale */,
				A18F8EB12E157214009D4427 /* ExhaleTests */,
				A18F8EBB2E157214009D4427 /* ExhaleUITests */,
				A18F8E9F2E157212009D4427 /* Products */,
			);
			sourceTree = "<group>";
		};
		A18F8E9F2E157212009D4427 /* Products */ = {
			isa = PBXGroup;
			children = (
				A18F8E9E2E157212009D4427 /* Exhale.app */,
				A18F8EAE2E157214009D4427 /* ExhaleTests.xctest */,
				A18F8EB82E157214009D4427 /* ExhaleUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A18F8E9D2E157212009D4427 /* Exhale */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A18F8EC22E157214009D4427 /* Build configuration list for PBXNativeTarget "Exhale" */;
			buildPhases = (
				A18F8E9A2E157212009D4427 /* Sources */,
				A18F8E9B2E157212009D4427 /* Frameworks */,
				A18F8E9C2E157212009D4427 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				A18F8EA02E157212009D4427 /* Exhale */,
			);
			name = Exhale;
			packageProductDependencies = (
			);
			productName = Exhale;
			productReference = A18F8E9E2E157212009D4427 /* Exhale.app */;
			productType = "com.apple.product-type.application";
		};
		A18F8EAD2E157214009D4427 /* ExhaleTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A18F8EC52E157214009D4427 /* Build configuration list for PBXNativeTarget "ExhaleTests" */;
			buildPhases = (
				A18F8EAA2E157214009D4427 /* Sources */,
				A18F8EAB2E157214009D4427 /* Frameworks */,
				A18F8EAC2E157214009D4427 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A18F8EB02E157214009D4427 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				A18F8EB12E157214009D4427 /* ExhaleTests */,
			);
			name = ExhaleTests;
			packageProductDependencies = (
			);
			productName = ExhaleTests;
			productReference = A18F8EAE2E157214009D4427 /* ExhaleTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		A18F8EB72E157214009D4427 /* ExhaleUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A18F8EC82E157214009D4427 /* Build configuration list for PBXNativeTarget "ExhaleUITests" */;
			buildPhases = (
				A18F8EB42E157214009D4427 /* Sources */,
				A18F8EB52E157214009D4427 /* Frameworks */,
				A18F8EB62E157214009D4427 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A18F8EBA2E157214009D4427 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				A18F8EBB2E157214009D4427 /* ExhaleUITests */,
			);
			name = ExhaleUITests;
			packageProductDependencies = (
			);
			productName = ExhaleUITests;
			productReference = A18F8EB82E157214009D4427 /* ExhaleUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A18F8E962E157212009D4427 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1610;
				LastUpgradeCheck = 1610;
				TargetAttributes = {
					A18F8E9D2E157212009D4427 = {
						CreatedOnToolsVersion = 16.1;
					};
					A18F8EAD2E157214009D4427 = {
						CreatedOnToolsVersion = 16.1;
						TestTargetID = A18F8E9D2E157212009D4427;
					};
					A18F8EB72E157214009D4427 = {
						CreatedOnToolsVersion = 16.1;
						TestTargetID = A18F8E9D2E157212009D4427;
					};
				};
			};
			buildConfigurationList = A18F8E992E157212009D4427 /* Build configuration list for PBXProject "Exhale" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A18F8E952E157212009D4427;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = A18F8E9F2E157212009D4427 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A18F8E9D2E157212009D4427 /* Exhale */,
				A18F8EAD2E157214009D4427 /* ExhaleTests */,
				A18F8EB72E157214009D4427 /* ExhaleUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A18F8E9C2E157212009D4427 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A18F8EAC2E157214009D4427 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A18F8EB62E157214009D4427 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A18F8E9A2E157212009D4427 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A18F8EAA2E157214009D4427 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A18F8EB42E157214009D4427 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		A18F8EB02E157214009D4427 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A18F8E9D2E157212009D4427 /* Exhale */;
			targetProxy = A18F8EAF2E157214009D4427 /* PBXContainerItemProxy */;
		};
		A18F8EBA2E157214009D4427 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A18F8E9D2E157212009D4427 /* Exhale */;
			targetProxy = A18F8EB92E157214009D4427 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		A18F8EC02E157214009D4427 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A18F8EC12E157214009D4427 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A18F8EC32E157214009D4427 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Exhale/Preview Content\"";
				DEVELOPMENT_TEAM = 37UFCDVH8Q;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = taglich.Exhale;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A18F8EC42E157214009D4427 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Exhale/Preview Content\"";
				DEVELOPMENT_TEAM = 37UFCDVH8Q;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = taglich.Exhale;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		A18F8EC62E157214009D4427 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 37UFCDVH8Q;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = taglich.ExhaleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Exhale.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Exhale";
			};
			name = Debug;
		};
		A18F8EC72E157214009D4427 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 37UFCDVH8Q;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = taglich.ExhaleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Exhale.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Exhale";
			};
			name = Release;
		};
		A18F8EC92E157214009D4427 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 37UFCDVH8Q;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = taglich.ExhaleUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Exhale;
			};
			name = Debug;
		};
		A18F8ECA2E157214009D4427 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 37UFCDVH8Q;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = taglich.ExhaleUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Exhale;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A18F8E992E157212009D4427 /* Build configuration list for PBXProject "Exhale" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A18F8EC02E157214009D4427 /* Debug */,
				A18F8EC12E157214009D4427 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A18F8EC22E157214009D4427 /* Build configuration list for PBXNativeTarget "Exhale" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A18F8EC32E157214009D4427 /* Debug */,
				A18F8EC42E157214009D4427 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A18F8EC52E157214009D4427 /* Build configuration list for PBXNativeTarget "ExhaleTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A18F8EC62E157214009D4427 /* Debug */,
				A18F8EC72E157214009D4427 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A18F8EC82E157214009D4427 /* Build configuration list for PBXNativeTarget "ExhaleUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A18F8EC92E157214009D4427 /* Debug */,
				A18F8ECA2E157214009D4427 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A18F8E962E157212009D4427 /* Project object */;
}
